#!/usr/bin/env python3
"""
OpenCV文本渲染技术验证

验证OpenCV是否可以替代PIL进行高性能文本渲染
"""

import cv2
import numpy as np
import time
from PIL import Image, ImageDraw, ImageFont
import os

def test_opencv_basic_text():
    """测试OpenCV基本文本渲染性能"""
    print("🔍 测试OpenCV基本文本渲染...")
    
    frame = np.zeros((1280, 720, 3), dtype=np.uint8)
    
    # 测试英文文本
    start_time = time.perf_counter()
    cv2.putText(frame, "Hello World", (100, 100), 
                cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 2)
    render_time = time.perf_counter() - start_time
    
    print(f"  英文文本渲染时间: {render_time:.6f}秒")
    
    # 测试中文文本（可能不支持）
    try:
        start_time = time.perf_counter()
        cv2.putText(frame, "你好世界", (100, 200), 
                    cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 0), 2)
        render_time = time.perf_counter() - start_time
        print(f"  中文文本渲染时间: {render_time:.6f}秒")
        chinese_support = True
    except Exception as e:
        print(f"  中文文本渲染失败: {e}")
        chinese_support = False
    
    return render_time < 0.001, chinese_support

def test_pil_text_performance():
    """测试PIL文本渲染性能作为对比"""
    print("\n🔍 测试PIL文本渲染性能（对比基准）...")
    
    # 创建PIL图像
    start_time = time.perf_counter()
    img = Image.new('RGB', (720, 1280), (0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 80)
    except:
        font = ImageFont.load_default()
    
    # 渲染文本
    draw.text((100, 100), "Hello World", fill=(255, 255, 255), font=font)
    draw.text((100, 200), "你好世界", fill=(255, 255, 0), font=font)
    
    # 转换为numpy数组
    frame = np.array(img)
    
    render_time = time.perf_counter() - start_time
    print(f"  PIL完整渲染时间: {render_time:.6f}秒")
    
    return render_time

def test_opencv_chinese_workaround():
    """测试OpenCV中文支持的变通方案"""
    print("\n🔍 测试OpenCV中文支持变通方案...")
    
    try:
        # 方案1：使用cv2.putText的unicode支持
        frame = np.zeros((1280, 720, 3), dtype=np.uint8)
        
        # 尝试不同的字体
        fonts = [
            cv2.FONT_HERSHEY_SIMPLEX,
            cv2.FONT_HERSHEY_PLAIN,
            cv2.FONT_HERSHEY_DUPLEX,
            cv2.FONT_HERSHEY_COMPLEX,
        ]
        
        for i, font in enumerate(fonts):
            try:
                start_time = time.perf_counter()
                cv2.putText(frame, "测试中文", (100, 100 + i * 50), 
                           font, 1, (255, 255, 255), 2)
                render_time = time.perf_counter() - start_time
                print(f"  字体 {font}: {render_time:.6f}秒")
                return True
            except Exception as e:
                print(f"  字体 {font} 失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"  变通方案失败: {e}")
        return False

def test_hybrid_approach():
    """测试PIL+OpenCV混合方案"""
    print("\n🔍 测试PIL+OpenCV混合方案...")
    
    try:
        # 创建主画布
        main_frame = np.zeros((1280, 720, 3), dtype=np.uint8)
        
        # 使用OpenCV绘制英文
        start_time = time.perf_counter()
        cv2.putText(main_frame, "English Text", (100, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 2)
        opencv_time = time.perf_counter() - start_time
        
        # 使用PIL绘制中文（最小化PIL使用）
        start_time = time.perf_counter()
        
        # 只为中文文本创建最小PIL图像
        try:
            font = ImageFont.truetype("arial.ttf", 80)
        except:
            font = ImageFont.load_default()
        
        # 计算文本尺寸
        bbox = font.getbbox("中文测试")
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 创建最小PIL图像
        text_img = Image.new('RGBA', (text_width + 10, text_height + 10), (0, 0, 0, 0))
        draw = ImageDraw.Draw(text_img)
        draw.text((5, 5), "中文测试", fill=(255, 255, 0, 255), font=font)
        
        # 转换为numpy并合成
        text_array = np.array(text_img)
        
        # 简单的alpha合成
        y_start, y_end = 200, 200 + text_height + 10
        x_start, x_end = 100, 100 + text_width + 10
        
        if y_end <= main_frame.shape[0] and x_end <= main_frame.shape[1]:
            alpha = text_array[:, :, 3:4].astype(np.float32) / 255.0
            main_frame[y_start:y_end, x_start:x_end] = (
                main_frame[y_start:y_end, x_start:x_end].astype(np.float32) * (1 - alpha) +
                text_array[:, :, :3].astype(np.float32) * alpha
            ).astype(np.uint8)
        
        pil_time = time.perf_counter() - start_time
        
        print(f"  OpenCV英文渲染: {opencv_time:.6f}秒")
        print(f"  PIL中文渲染+合成: {pil_time:.6f}秒")
        print(f"  总时间: {opencv_time + pil_time:.6f}秒")
        
        return opencv_time + pil_time < 0.01
        
    except Exception as e:
        print(f"  混合方案失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """性能对比测试"""
    print("\n📊 性能对比测试...")
    
    # 测试场景：渲染10个文本片段
    texts = [
        "第一句歌词",
        "第二句歌词", 
        "第三句歌词",
        "English Lyric 1",
        "English Lyric 2",
        "混合文本 Mixed Text",
        "Performance Test",
        "性能测试",
        "Final Test",
        "最终测试"
    ]
    
    # OpenCV方案（如果支持中文）
    print("  测试OpenCV方案...")
    start_time = time.perf_counter()
    frame = np.zeros((1280, 720, 3), dtype=np.uint8)
    
    for i, text in enumerate(texts):
        try:
            cv2.putText(frame, text, (100, 50 + i * 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        except:
            # 如果中文失败，跳过
            pass
    
    opencv_total_time = time.perf_counter() - start_time
    print(f"    OpenCV总时间: {opencv_total_time:.6f}秒")
    print(f"    平均每个文本: {opencv_total_time/len(texts):.6f}秒")
    
    # PIL方案
    print("  测试PIL方案...")
    start_time = time.perf_counter()
    
    img = Image.new('RGB', (720, 1280), (0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        font = ImageFont.load_default()
    
    for i, text in enumerate(texts):
        draw.text((100, 50 + i * 60), text, fill=(255, 255, 255), font=font)
    
    frame = np.array(img)
    
    pil_total_time = time.perf_counter() - start_time
    print(f"    PIL总时间: {pil_total_time:.6f}秒")
    print(f"    平均每个文本: {pil_total_time/len(texts):.6f}秒")
    
    # 性能比较
    if opencv_total_time > 0:
        speedup = pil_total_time / opencv_total_time
        print(f"    OpenCV相对PIL加速: {speedup:.1f}倍")
    
    return opencv_total_time, pil_total_time

def main():
    """主测试函数"""
    print("🚀 OpenCV文本渲染技术验证")
    print("=" * 50)
    
    # 测试1：基本功能
    opencv_fast, chinese_support = test_opencv_basic_text()
    
    # 测试2：PIL性能基准
    pil_time = test_pil_text_performance()
    
    # 测试3：中文支持变通方案
    chinese_workaround = test_opencv_chinese_workaround()
    
    # 测试4：混合方案
    hybrid_works = test_hybrid_approach()
    
    # 测试5：性能对比
    opencv_time, pil_time = test_performance_comparison()
    
    # 总结报告
    print("\n" + "=" * 50)
    print("📋 技术验证总结报告")
    print("=" * 50)
    
    print(f"✅ OpenCV基本渲染: {'通过' if opencv_fast else '失败'}")
    print(f"✅ OpenCV中文支持: {'原生支持' if chinese_support else '需要变通'}")
    print(f"✅ 中文变通方案: {'可行' if chinese_workaround else '不可行'}")
    print(f"✅ PIL+OpenCV混合: {'可行' if hybrid_works else '不可行'}")
    
    if opencv_time > 0 and pil_time > 0:
        speedup = pil_time / opencv_time
        print(f"📊 性能提升: {speedup:.1f}倍")
    
    # 推荐方案
    print("\n🎯 推荐技术方案:")
    if chinese_support:
        print("   方案1: 纯OpenCV渲染（最优）")
    elif hybrid_works:
        print("   方案2: OpenCV+PIL混合渲染（推荐）")
    else:
        print("   方案3: 优化的PIL渲染（备选）")
    
    # 是否达到性能目标
    target_time = 0.01  # 10毫秒目标
    if hybrid_works and (opencv_time + pil_time/10) < target_time:
        print(f"✅ 性能目标: 可达成（预计 {(opencv_time + pil_time/10)*1000:.1f}ms/帧）")
    else:
        print(f"⚠️  性能目标: 需要进一步优化")

if __name__ == "__main__":
    main()
