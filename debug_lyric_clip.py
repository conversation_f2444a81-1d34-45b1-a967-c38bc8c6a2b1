#!/usr/bin/env python3
"""
调试LyricClip的简化测试
"""

import time
import numpy as np
from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle
from layout_engine import LayoutEngine, VerticalStackStrategy
from lyric_clip import create_lyric_clip

def test_single_frame_render():
    """测试单帧渲染是否正常"""
    print("🔍 测试单帧渲染（预渲染优化版）...")

    # 创建简单的测试数据
    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词")
    ]

    # 创建时间轴
    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        style=LyricStyle(font_size=80),
        element_id="test_timeline"
    )

    # 创建布局引擎
    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=30))
    layout_engine.add_element(timeline)

    # 创建LyricClip
    print("创建LyricClip（包含预渲染）...")
    start_time = time.perf_counter()

    try:
        lyric_clip = create_lyric_clip(
            timelines=[timeline],
            layout_engine=layout_engine,
            size=(720, 1280),
            duration=10.0,
            fps=30
        )

        creation_time = time.perf_counter() - start_time
        print(f"✅ LyricClip创建成功: {creation_time:.3f}秒")

        # 测试单帧渲染
        print("测试单帧渲染（应该很快）...")
        frame_start = time.perf_counter()

        frame = lyric_clip.get_frame(1.0)  # 渲染第1秒的帧

        frame_time = time.perf_counter() - frame_start
        print(f"✅ 单帧渲染成功: {frame_time:.6f}秒, 帧尺寸: {frame.shape}")

        # 测试多个时间点
        print("测试多个时间点...")
        test_times = [0.5, 1.5, 3.5, 6.5]

        total_frame_time = 0
        for t in test_times:
            frame_start = time.perf_counter()
            frame = lyric_clip.get_frame(t)
            frame_time = time.perf_counter() - frame_start
            total_frame_time += frame_time
            print(f"  时间 {t}s: {frame_time:.6f}秒, 帧尺寸: {frame.shape}")

            if frame_time > 0.01:  # 如果单帧渲染超过10毫秒，说明还有优化空间
                print(f"⚠️  注意：时间 {t}s 的帧渲染时间: {frame_time:.6f}秒")

        avg_frame_time = total_frame_time / len(test_times)
        print(f"📊 平均帧渲染时间: {avg_frame_time:.6f}秒")

        # 估算30秒视频的渲染时间
        estimated_time = avg_frame_time * 30 * 30  # 30秒 * 30fps
        print(f"📊 估算30秒视频渲染时间: {estimated_time:.1f}秒")

        return True

    except Exception as e:
        error_time = time.perf_counter() - start_time
        print(f"❌ 测试失败: {e} (耗时: {error_time:.3f}秒)")
        import traceback
        traceback.print_exc()
        return False

def test_timeline_content_query():
    """测试时间轴内容查询是否正常"""
    print("\n🔍 测试时间轴内容查询...")

    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词")
    ]

    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        style=LyricStyle(font_size=80)
    )

    test_times = [0.5, 1.5, 3.5, 6.5, 9.0]

    for t in test_times:
        start_time = time.perf_counter()
        content = timeline.get_content_at_time(t)
        query_time = time.perf_counter() - start_time

        if content:
            print(f"  时间 {t}s: '{content['text']}' (查询耗时: {query_time:.6f}秒)")
        else:
            print(f"  时间 {t}s: 无歌词 (查询耗时: {query_time:.6f}秒)")

        if query_time > 0.001:  # 如果查询超过1毫秒，说明有问题
            print(f"⚠️  警告：时间 {t}s 的查询时间过长: {query_time:.6f}秒")

def test_enhanced_generator():
    """测试enhanced_generator是否正常"""
    print("\n🔍 测试enhanced_generator...")

    try:
        from enhanced_generator import EnhancedJingwuGenerator

        generator = EnhancedJingwuGenerator(720, 1280, 30)

        start_time = time.perf_counter()
        text_img = generator.create_enhanced_text_image(
            text="测试文本",
            font_size=80,
            color="#FFD700",
            width=720,
            height=1280,
            y_position=640,
            glow=True
        )

        render_time = time.perf_counter() - start_time
        print(f"✅ enhanced_generator渲染成功: {render_time:.3f}秒, 图像尺寸: {text_img.shape}")

        if render_time > 0.5:  # 如果渲染超过0.5秒，说明有问题
            print(f"⚠️  警告：文本渲染时间过长: {render_time:.3f}秒")

        return True

    except Exception as e:
        print(f"❌ enhanced_generator测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 LyricClip调试测试")
    print("=" * 50)

    # 测试1：时间轴内容查询
    test_timeline_content_query()

    # 测试2：enhanced_generator
    if not test_enhanced_generator():
        print("❌ enhanced_generator测试失败，停止后续测试")
        return

    # 测试3：单帧渲染
    if not test_single_frame_render():
        print("❌ 单帧渲染测试失败")
        return

    print("\n✅ 所有测试通过！")

if __name__ == "__main__":
    main()
