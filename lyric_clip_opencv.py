"""
LyricClip OpenCV重构版本

使用OpenCV直接帧渲染，消除PIL依赖，实现真正的高性能歌词渲染
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
from moviepy import VideoClip

from lyric_timeline import LyricTimeline
from layout_engine import LayoutEngine
from layout_types import LyricRect


class FontCache:
    """字体缓存系统，避免重复计算"""
    _text_size_cache: Dict[Tuple[str, float, int], Tuple[int, int]] = {}
    
    @classmethod
    def get_text_size(cls, text: str, font_scale: float, thickness: int) -> Tuple[int, int]:
        """获取文本尺寸，带缓存"""
        key = (text, font_scale, thickness)
        if key not in cls._text_size_cache:
            (width, height), baseline = cv2.getTextSize(
                text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)
            cls._text_size_cache[key] = (width, height + baseline)
        return cls._text_size_cache[key]
    
    @classmethod
    def clear_cache(cls):
        """清空缓存"""
        cls._text_size_cache.clear()


class LyricClipOpenCV(VideoClip):
    """基于OpenCV的高性能歌词视频片段容器
    
    核心特性：
    - 使用OpenCV直接帧渲染，消除PIL依赖
    - 单一frame_function处理所有歌词时间轴
    - 字体缓存系统，避免重复计算
    - 预期性能：<0.01秒/帧
    """

    def __init__(self,
                 timelines: List[LyricTimeline],
                 layout_engine: LayoutEngine,
                 size: Tuple[int, int],
                 duration: float,
                 fps: float = 30):
        """初始化LyricClip
        
        Args:
            timelines: 歌词时间轴列表
            layout_engine: 布局引擎
            size: 视频尺寸 (width, height)
            duration: 视频时长
            fps: 帧率
        """
        self.timelines = timelines
        self.layout_engine = layout_engine
        self.video_size = size
        self.fps = fps
        
        # 预计算布局
        self.layout_result = layout_engine.calculate_layout(size[0], size[1])
        
        # 创建时间轴到布局位置的映射
        self._timeline_positions = {}
        for timeline in timelines:
            if timeline.element_id in self.layout_result.element_positions:
                self._timeline_positions[timeline.element_id] = \
                    self.layout_result.element_positions[timeline.element_id]
        
        # 预计算字体参数，避免运行时计算
        self._font_params = self._precompute_font_params()
        
        print(f"✅ LyricClipOpenCV初始化完成: {len(timelines)}个时间轴, {len(self._font_params)}个字体配置")
        
        # 初始化VideoClip，使用frame_function
        super().__init__(
            frame_function=self._render_frame,
            duration=duration,
            has_constant_size=True
        )
        self.size = size
        self.fps = fps

    def _precompute_font_params(self) -> Dict[str, Dict]:
        """预计算字体参数，避免运行时重复计算"""
        font_params = {}
        
        for timeline in self.timelines:
            timeline_id = timeline.element_id
            font_size = timeline.style.font_size
            
            # 计算OpenCV字体参数
            font_scale = font_size / 50.0  # 经验值，可调整
            thickness = max(1, int(font_size / 30))  # 根据字体大小调整粗细
            
            # 获取颜色
            highlight_color = getattr(timeline.style, 'highlight_color', '#FFD700')
            normal_color = getattr(timeline.style, 'normal_color', '#FFFFFF')
            
            font_params[timeline_id] = {
                'font_scale': font_scale,
                'thickness': thickness,
                'highlight_color': self._hex_to_bgr(highlight_color),
                'normal_color': self._hex_to_bgr(normal_color),
                'shadow_color': (0, 0, 0),  # 黑色阴影
                'shadow_offset': max(1, thickness // 2)
            }
        
        return font_params

    def _hex_to_bgr(self, hex_color: str) -> Tuple[int, int, int]:
        """将十六进制颜色转换为BGR格式（OpenCV使用BGR）"""
        if hex_color.startswith('#'):
            hex_color = hex_color[1:]
        
        # 解析RGB
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        
        # 返回BGR格式
        return (b, g, r)

    def _render_frame(self, t: float) -> np.ndarray:
        """核心渲染方法：使用OpenCV直接渲染帧
        
        这是性能关键路径，目标：<0.01秒/帧
        
        Args:
            t: 当前时间
            
        Returns:
            渲染的帧数据 (height, width, 3)
        """
        # 创建空白画布（RGB格式）
        frame = np.zeros((self.video_size[1], self.video_size[0], 3), dtype=np.uint8)
        
        # 遍历所有时间轴，直接渲染到frame上
        for timeline in self.timelines:
            self._render_timeline_opencv(frame, timeline, t)
        
        return frame

    def _render_timeline_opencv(self, frame: np.ndarray, 
                               timeline: LyricTimeline, 
                               t: float):
        """使用OpenCV直接渲染时间轴内容到frame上
        
        Args:
            frame: 目标帧数组
            timeline: 歌词时间轴
            t: 当前时间
        """
        timeline_id = timeline.element_id
        
        # 获取布局位置
        layout_rect = self._timeline_positions.get(timeline_id)
        if not layout_rect:
            return
        
        # 获取当前时间的歌词内容
        lyric_content = timeline.get_content_at_time(t)
        if not lyric_content:
            return
        
        # 获取预计算的字体参数
        font_params = self._font_params.get(timeline_id)
        if not font_params:
            return
        
        # 渲染文本
        self._draw_text_opencv(frame, lyric_content, layout_rect, font_params, t)

    def _draw_text_opencv(self, frame: np.ndarray, 
                         lyric_content: Dict, 
                         layout_rect: LyricRect,
                         font_params: Dict,
                         t: float):
        """使用OpenCV绘制文本
        
        Args:
            frame: 目标帧数组
            lyric_content: 歌词内容字典
            layout_rect: 布局区域
            font_params: 字体参数
            t: 当前时间
        """
        text = lyric_content['text']
        start_time = lyric_content['start_time']
        duration = lyric_content['duration']
        
        # 计算动画进度（淡入淡出）
        animation_progress = self._calculate_animation_progress(t, start_time, duration)
        if animation_progress <= 0:
            return
        
        # 处理多行文本
        lines = text.split('\n')
        if not lines:
            return
        
        # 获取字体参数
        font_scale = font_params['font_scale']
        thickness = font_params['thickness']
        shadow_offset = font_params['shadow_offset']
        
        # 计算总文本高度
        line_height = int(font_params['font_scale'] * 50 * 1.2)  # 行高
        total_height = len(lines) * line_height
        
        # 计算起始Y位置（垂直居中）
        start_y = layout_rect.y + (layout_rect.height - total_height) // 2 + line_height
        
        # 逐行绘制
        for i, line in enumerate(lines):
            if not line.strip():
                continue
            
            # 计算当前行位置
            y = start_y + i * line_height
            
            # 获取文本尺寸
            text_width, text_height = FontCache.get_text_size(line, font_scale, thickness)
            
            # 水平居中
            x = layout_rect.x + (layout_rect.width - text_width) // 2
            
            # 确保位置在帧范围内
            if x < 0 or y < 0 or x + text_width > frame.shape[1] or y > frame.shape[0]:
                continue
            
            # 应用透明度
            alpha = int(255 * animation_progress)
            shadow_color = (*font_params['shadow_color'], alpha)
            text_color = (*font_params['highlight_color'], alpha)
            
            # 绘制阴影
            cv2.putText(frame, line, 
                       (x + shadow_offset, y + shadow_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 
                       font_scale, 
                       font_params['shadow_color'], 
                       thickness)
            
            # 绘制主文本
            cv2.putText(frame, line,
                       (x, y),
                       cv2.FONT_HERSHEY_SIMPLEX,
                       font_scale,
                       font_params['highlight_color'],
                       thickness)

    def _calculate_animation_progress(self, t: float, start_time: float, 
                                    duration: float, fade_duration: float = 0.3) -> float:
        """计算动画进度（淡入淡出）
        
        Args:
            t: 当前时间
            start_time: 开始时间
            duration: 持续时间
            fade_duration: 淡入淡出时间
            
        Returns:
            动画进度 (0.0-1.0)
        """
        if t < start_time or t >= start_time + duration:
            return 0.0
        
        relative_time = t - start_time
        
        # 淡入
        if relative_time <= fade_duration:
            return relative_time / fade_duration
        
        # 淡出
        fade_out_start = duration - fade_duration
        if relative_time >= fade_out_start:
            fade_out_progress = (relative_time - fade_out_start) / fade_duration
            return 1.0 - fade_out_progress
        
        # 完全显示
        return 1.0


def create_lyric_clip_opencv(timelines: List[LyricTimeline],
                           layout_engine: LayoutEngine,
                           size: Tuple[int, int],
                           duration: float,
                           fps: float = 30) -> LyricClipOpenCV:
    """创建基于OpenCV的LyricClip
    
    Args:
        timelines: 歌词时间轴列表
        layout_engine: 布局引擎
        size: 视频尺寸
        duration: 视频时长
        fps: 帧率
        
    Returns:
        LyricClipOpenCV实例
    """
    return LyricClipOpenCV(timelines, layout_engine, size, duration, fps)
